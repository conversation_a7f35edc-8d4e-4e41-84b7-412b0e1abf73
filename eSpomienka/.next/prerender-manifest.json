{"version": 4, "routes": {"/balicky/zakladna": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/d9vkRoXm85xjayO-RTCnR/balicky/zakladna.json"}, "/balicky/premium": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/d9vkRoXm85xjayO-RTCnR/balicky/premium.json"}, "/balicky/exclusive": {"initialRevalidateSeconds": false, "srcRoute": "/balicky/[slug]", "dataRoute": "/_next/data/d9vkRoXm85xjayO-RTCnR/balicky/exclusive.json"}}, "dynamicRoutes": {"/balicky/[slug]": {"routeRegex": "^/balicky/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/d9vkRoXm85xjayO-RTCnR/balicky/[slug].json", "fallback": false, "dataRouteRegex": "^/_next/data/d9vkRoXm85xjayO\\-RTCnR/balicky/([^/]+?)\\.json$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "33ae229856481ec0c72b742e0983b4e7", "previewModeSigningKey": "268b81a61671e68a3336839a1ab081b816c47e74059cbbac855961459d75bf53", "previewModeEncryptionKey": "aa56a5e3fb7c77566ae2dd72f2589f57c5f10946111ced5169e822e1e1aea63c"}}